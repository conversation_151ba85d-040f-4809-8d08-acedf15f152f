@tailwind base;
@tailwind components;
@tailwind utilities;

/* MeatE Design System - Premium Meat E-commerce Platform */

@layer base {
  :root {
    /* Core Brand Colors */
    --background: 0 0% 100%;
    --foreground: 0 0% 20%;

    --card: 0 0% 100%;
    --card-foreground: 0 0% 20%;

    --popover: 0 0% 100%;
    --popover-foreground: 0 0% 20%;

    /* Primary - Meat Red */
    --primary: 8 94% 47%;
    --primary-foreground: 0 0% 98%;
    --primary-hover: 8 94% 42%;

    /* Secondary - Dark Gray */
    --secondary: 0 0% 20%;
    --secondary-foreground: 0 0% 98%;

    /* Accent - Golden Yellow */
    --accent: 48 96% 58%;
    --accent-foreground: 0 0% 20%;

    /* Neutral Grays */
    --muted: 0 0% 97.5%;
    --muted-foreground: 0 0% 45%;

    /* Success & Error */
    --success: 142 76% 36%;
    --success-foreground: 0 0% 98%;
    --destructive: 0 84% 60%;
    --destructive-foreground: 0 0% 98%;

    /* UI Elements */
    --border: 0 0% 90%;
    --input: 0 0% 100%;
    --ring: 8 94% 47%;

    /* Gradients */
    --gradient-hero: linear-gradient(135deg, hsl(8 94% 47% / 0.1), hsl(48 96% 58% / 0.1));
    --gradient-card: linear-gradient(145deg, hsl(0 0% 100%), hsl(0 0% 98%));

    /* Shadows */
    --shadow-soft: 0 2px 8px hsl(0 0% 0% / 0.08);
    --shadow-medium: 0 4px 16px hsl(0 0% 0% / 0.12);
    --shadow-large: 0 8px 24px hsl(0 0% 0% / 0.16);

    --radius: 0.75rem;

    --sidebar-background: 0 0% 98%;

    --sidebar-foreground: 240 5.3% 26.1%;

    --sidebar-primary: 240 5.9% 10%;

    --sidebar-primary-foreground: 0 0% 98%;

    --sidebar-accent: 240 4.8% 95.9%;

    --sidebar-accent-foreground: 240 5.9% 10%;

    --sidebar-border: 220 13% 91%;

    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;

    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;

    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 11.2%;

    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground font-inter;
  }
}

@layer components {
  /* Typography System */
  .heading-xl {
    @apply font-merriweather font-bold text-4xl md:text-5xl lg:text-6xl leading-tight;
  }

  .heading-lg {
    @apply font-inter font-semibold text-2xl md:text-3xl lg:text-4xl leading-tight;
  }

  .heading-md {
    @apply font-inter font-medium text-lg md:text-xl lg:text-2xl leading-normal;
  }

  .body-text {
    @apply font-inter font-normal text-sm md:text-base leading-relaxed;
  }

  .caption {
    @apply font-inter font-normal text-xs leading-normal;
  }

  /* Button Variants */
  .btn-primary {
    @apply bg-primary hover:bg-primary/90 text-primary-foreground font-medium px-6 py-3 rounded-lg transition-all duration-200 shadow-soft hover:shadow-medium;
  }

  .btn-secondary {
    @apply bg-secondary hover:bg-secondary/90 text-secondary-foreground font-medium px-6 py-3 rounded-lg transition-all duration-200;
  }

  .btn-outline {
    @apply border-2 border-primary text-primary hover:bg-primary hover:text-primary-foreground font-medium px-6 py-3 rounded-lg transition-all duration-200;
  }

  /* Card Styles */
  .card-elevated {
    @apply bg-card rounded-xl shadow-soft hover:shadow-medium transition-all duration-200;
  }

  .card-product {
    @apply card-elevated p-4 cursor-pointer;
  }

  /* Gradient Backgrounds */
  .bg-hero-gradient {
    background: var(--gradient-hero);
  }

  .bg-card-gradient {
    background: var(--gradient-card);
  }
}